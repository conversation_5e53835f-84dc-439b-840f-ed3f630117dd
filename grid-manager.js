// Grid management utilities for Screenshot Array Extension

class GridManager {
    constructor() {
        this.grid = {};
        this.maxRows = 10;
        this.maxCols = 10;
        this.cellPadding = 0;
        this.backgroundColor = '#ffffff';
    }

    /**
     * Add screenshot to grid at specified position
     */
    addScreenshot(row, col, imageData, dimensions) {
        if (row < 0 || col < 0 || row > this.maxRows || col > this.maxCols) {
            throw new Error(`Position ${row},${col} is out of bounds (max: ${this.maxRows}x${this.maxCols})`);
        }

        const key = `${row}-${col}`;
        this.grid[key] = {
            row,
            col,
            imageData,
            dimensions,
            timestamp: Date.now()
        };

        return key;
    }

    /**
     * Remove screenshot from grid
     */
    removeScreenshot(row, col) {
        const key = `${row}-${col}`;
        if (this.grid[key]) {
            delete this.grid[key];
            return true;
        }
        return false;
    }

    /**
     * Get screenshot at position
     */
    getScreenshot(row, col) {
        const key = `${row}-${col}`;
        return this.grid[key] || null;
    }

    /**
     * Check if position is occupied
     */
    isPositionOccupied(row, col) {
        const key = `${row}-${col}`;
        return !!this.grid[key];
    }

    /**
     * Get all screenshots in grid
     */
    getAllScreenshots() {
        return { ...this.grid };
    }

    /**
     * Clear entire grid
     */
    clearGrid() {
        this.grid = {};
    }

    /**
     * Get grid dimensions (actual used area)
     */
    getGridDimensions() {
        if (Object.keys(this.grid).length === 0) {
            return { rows: 0, cols: 0, minRow: 0, minCol: 0, maxRow: 0, maxCol: 0 };
        }

        let minRow = Infinity, maxRow = -Infinity;
        let minCol = Infinity, maxCol = -Infinity;

        Object.values(this.grid).forEach(item => {
            minRow = Math.min(minRow, item.row);
            maxRow = Math.max(maxRow, item.row);
            minCol = Math.min(minCol, item.col);
            maxCol = Math.max(maxCol, item.col);
        });

        return {
            rows: maxRow - minRow + 1,
            cols: maxCol - minCol + 1,
            minRow,
            minCol,
            maxRow,
            maxCol
        };
    }

    /**
     * Calculate optimal cell size for combined image
     */
    calculateOptimalCellSize() {
        if (Object.keys(this.grid).length === 0) {
            return { width: 200, height: 150 };
        }

        let maxWidth = 0, maxHeight = 0;
        let totalWidth = 0, totalHeight = 0;
        let count = 0;

        Object.values(this.grid).forEach(item => {
            if (item.dimensions) {
                maxWidth = Math.max(maxWidth, item.dimensions.width);
                maxHeight = Math.max(maxHeight, item.dimensions.height);
                totalWidth += item.dimensions.width;
                totalHeight += item.dimensions.height;
                count++;
            }
        });

        // Use the maximum dimensions to ensure all images fit
        return {
            width: maxWidth || 200,
            height: maxHeight || 150,
            avgWidth: count > 0 ? Math.round(totalWidth / count) : 200,
            avgHeight: count > 0 ? Math.round(totalHeight / count) : 150
        };
    }

    /**
     * Generate combined image from grid
     */
    async generateCombinedImage(options = {}) {
        const {
            cellWidth = null,
            cellHeight = null,
            padding = this.cellPadding,
            backgroundColor = this.backgroundColor,
            includeEmptyCells = false,
            format = 'png',
            quality = 0.9
        } = options;

        if (Object.keys(this.grid).length === 0) {
            throw new Error('No screenshots in grid to combine');
        }

        const dimensions = this.getGridDimensions();
        const cellSize = this.calculateOptimalCellSize();
        
        const finalCellWidth = cellWidth || cellSize.width;
        const finalCellHeight = cellHeight || cellSize.height;

        // Create canvas for combined image
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Calculate canvas size
        canvas.width = dimensions.cols * finalCellWidth + (dimensions.cols - 1) * padding;
        canvas.height = dimensions.rows * finalCellHeight + (dimensions.rows - 1) * padding;

        // Fill background
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Load and draw images
        const imagePromises = [];

        for (let row = dimensions.minRow; row <= dimensions.maxRow; row++) {
            for (let col = dimensions.minCol; col <= dimensions.maxCol; col++) {
                const screenshot = this.getScreenshot(row, col);
                
                if (screenshot) {
                    const promise = this.drawImageToCanvas(
                        ctx,
                        screenshot.imageData,
                        (col - dimensions.minCol) * (finalCellWidth + padding),
                        (row - dimensions.minRow) * (finalCellHeight + padding),
                        finalCellWidth,
                        finalCellHeight
                    );
                    imagePromises.push(promise);
                } else if (includeEmptyCells) {
                    // Draw empty cell placeholder
                    const x = (col - dimensions.minCol) * (finalCellWidth + padding);
                    const y = (row - dimensions.minRow) * (finalCellHeight + padding);
                    
                    ctx.fillStyle = '#f8f9fa';
                    ctx.fillRect(x, y, finalCellWidth, finalCellHeight);
                    
                    ctx.strokeStyle = '#dee2e6';
                    ctx.lineWidth = 1;
                    ctx.strokeRect(x, y, finalCellWidth, finalCellHeight);
                    
                    // Add position label
                    ctx.fillStyle = '#6c757d';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(`${row},${col}`, x + finalCellWidth / 2, y + finalCellHeight / 2);
                }
            }
        }

        // Wait for all images to be drawn
        await Promise.all(imagePromises);

        // Return image data
        const mimeType = format === 'jpg' || format === 'jpeg' ? 'image/jpeg' : 'image/png';
        return canvas.toDataURL(mimeType, quality);
    }

    /**
     * Draw image to canvas at specified position
     */
    drawImageToCanvas(ctx, imageData, x, y, width, height) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            
            img.onload = () => {
                try {
                    ctx.drawImage(img, x, y, width, height);
                    resolve();
                } catch (error) {
                    reject(error);
                }
            };
            
            img.onerror = () => {
                reject(new Error('Failed to load image'));
            };
            
            img.src = imageData;
        });
    }

    /**
     * Export grid data for storage
     */
    exportGridData() {
        return {
            grid: this.grid,
            settings: {
                maxRows: this.maxRows,
                maxCols: this.maxCols,
                cellPadding: this.cellPadding,
                backgroundColor: this.backgroundColor
            },
            metadata: {
                totalScreenshots: Object.keys(this.grid).length,
                dimensions: this.getGridDimensions(),
                exportDate: new Date().toISOString()
            }
        };
    }

    /**
     * Import grid data from storage
     */
    importGridData(data) {
        if (data.grid) {
            this.grid = data.grid;
        }
        
        if (data.settings) {
            this.maxRows = data.settings.maxRows || this.maxRows;
            this.maxCols = data.settings.maxCols || this.maxCols;
            this.cellPadding = data.settings.cellPadding || this.cellPadding;
            this.backgroundColor = data.settings.backgroundColor || this.backgroundColor;
        }
    }

    /**
     * Get grid statistics
     */
    getGridStats() {
        const dimensions = this.getGridDimensions();
        const screenshots = Object.values(this.grid);
        
        let totalSize = 0;
        screenshots.forEach(screenshot => {
            if (screenshot.imageData) {
                totalSize += screenshot.imageData.length;
            }
        });

        return {
            totalScreenshots: screenshots.length,
            gridDimensions: dimensions,
            estimatedSize: Math.round(totalSize / 1024) + ' KB',
            oldestScreenshot: screenshots.length > 0 ? Math.min(...screenshots.map(s => s.timestamp)) : null,
            newestScreenshot: screenshots.length > 0 ? Math.max(...screenshots.map(s => s.timestamp)) : null
        };
    }

    /**
     * Validate grid integrity
     */
    validateGrid() {
        const errors = [];
        
        Object.entries(this.grid).forEach(([key, screenshot]) => {
            const expectedKey = `${screenshot.row}-${screenshot.col}`;
            if (key !== expectedKey) {
                errors.push(`Key mismatch: ${key} vs ${expectedKey}`);
            }
            
            if (!screenshot.imageData) {
                errors.push(`Missing image data for position ${screenshot.row},${screenshot.col}`);
            }
            
            if (!screenshot.dimensions) {
                errors.push(`Missing dimensions for position ${screenshot.row},${screenshot.col}`);
            }
        });
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GridManager;
} else {
    window.GridManager = GridManager;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Screenshot Array Extension - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }

        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .demo-areas {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 30px 0;
        }

        .demo-area {
            height: 150px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #666;
            background: rgba(255, 255, 255, 0.5);
        }

        .demo-area:nth-child(1) { background: rgba(255, 107, 107, 0.3); }
        .demo-area:nth-child(2) { background: rgba(78, 205, 196, 0.3); }
        .demo-area:nth-child(3) { background: rgba(69, 183, 209, 0.3); }
        .demo-area:nth-child(4) { background: rgba(150, 206, 180, 0.3); }
        .demo-area:nth-child(5) { background: rgba(255, 193, 7, 0.3); }
        .demo-area:nth-child(6) { background: rgba(220, 53, 69, 0.3); }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Screenshot Array Extension Test Page</h1>
        
        <div class="instructions">
            <h3>📋 How to Test the Extension:</h3>
            <ol>
                <li><strong>Install the Extension</strong> - Load it in Chrome Developer Mode</li>
                <li><strong>Click the Extension Icon</strong> - It should appear in your toolbar</li>
                <li><strong>Enter Grid Position</strong> - Start with Row: 0, Column: 0</li>
                <li><strong>Click "Start Capture"</strong> - The screen should freeze with an overlay</li>
                <li><strong>Select an Area</strong> - Click and drag to select any rectangular area below</li>
                <li><strong>Repeat</strong> - Try different positions like (0,1), (1,0), (1,1)</li>
                <li><strong>Export</strong> - Click "Export Combined Image" to see your grid</li>
            </ol>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🎯 Feature 1: Precise Selection</h3>
                <p>The extension allows you to select any rectangular area with pixel-perfect precision. The selection boundaries are visible during capture but not included in the final screenshot.</p>
            </div>
            
            <div class="card">
                <h3>🔢 Feature 2: Grid Organization</h3>
                <p>Organize your screenshots in a grid using row and column coordinates. Position (0,0) represents the top-left corner of your final combined image.</p>
            </div>
            
            <div class="card">
                <h3>🖼️ Feature 3: Combined Export</h3>
                <p>Automatically combines all your screenshots into a single image, perfectly arranged according to their grid positions.</p>
            </div>
        </div>

        <h2>🎨 Demo Areas for Testing</h2>
        <p>Try capturing these different colored areas to test the extension:</p>
        
        <div class="demo-areas">
            <div class="demo-area">Area 1<br>Position (0,0)</div>
            <div class="demo-area">Area 2<br>Position (0,1)</div>
            <div class="demo-area">Area 3<br>Position (0,2)</div>
            <div class="demo-area">Area 4<br>Position (1,0)</div>
            <div class="demo-area">Area 5<br>Position (1,1)</div>
            <div class="demo-area">Area 6<br>Position (1,2)</div>
        </div>

        <div class="card">
            <h3>🔧 Troubleshooting</h3>
            <ul>
                <li><strong>No overlay appears:</strong> Make sure you're not on a chrome:// page</li>
                <li><strong>Can't select area:</strong> Try refreshing the page and capturing again</li>
                <li><strong>Permission denied:</strong> Ensure the extension has screenshot permissions</li>
                <li><strong>Export not working:</strong> Make sure you have at least one screenshot captured</li>
            </ul>
        </div>

        <div class="footer">
            <p><strong>Screenshot Array Extension</strong> - Created by Moond Sahab</p>
            <p>Perfect for creating organized screenshot collections and documentation</p>
        </div>
    </div>

    <script>
        // Add some interactivity for testing
        document.querySelectorAll('.demo-area').forEach((area, index) => {
            area.addEventListener('click', () => {
                area.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    area.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // Log when page is ready
        console.log('Screenshot Array Extension test page loaded');
        console.log('Ready for testing! Click the extension icon to start.');
    </script>
</body>
</html>

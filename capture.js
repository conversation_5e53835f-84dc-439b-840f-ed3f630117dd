// Screen capture utilities for Screenshot Array Extension

class ScreenCaptureUtils {
    constructor() {
        this.mediaStream = null;
        this.videoElement = null;
    }

    /**
     * Initialize screen capture with proper permissions
     */
    async initializeCapture() {
        try {
            // Request screen capture permission
            this.mediaStream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    mediaSource: 'screen',
                    width: { ideal: 1920 },
                    height: { ideal: 1080 }
                },
                audio: false
            });

            // Create video element to capture frames
            this.videoElement = document.createElement('video');
            this.videoElement.srcObject = this.mediaStream;
            this.videoElement.autoplay = true;
            this.videoElement.muted = true;
            this.videoElement.style.display = 'none';
            document.body.appendChild(this.videoElement);

            return new Promise((resolve) => {
                this.videoElement.addEventListener('loadedmetadata', () => {
                    resolve(true);
                });
            });

        } catch (error) {
            console.error('Failed to initialize screen capture:', error);
            throw new Error('Screen capture permission denied or not supported');
        }
    }

    /**
     * Capture a specific area of the screen
     */
    async captureArea(x, y, width, height) {
        if (!this.videoElement || !this.mediaStream) {
            throw new Error('Screen capture not initialized');
        }

        try {
            // Create canvas for capture
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = width;
            canvas.height = height;

            // Calculate scale factors
            const videoWidth = this.videoElement.videoWidth;
            const videoHeight = this.videoElement.videoHeight;
            const screenWidth = window.screen.width;
            const screenHeight = window.screen.height;
            
            const scaleX = videoWidth / screenWidth;
            const scaleY = videoHeight / screenHeight;

            // Adjust coordinates for video scale
            const scaledX = x * scaleX;
            const scaledY = y * scaleY;
            const scaledWidth = width * scaleX;
            const scaledHeight = height * scaleY;

            // Draw the selected area from video to canvas
            ctx.drawImage(
                this.videoElement,
                scaledX, scaledY, scaledWidth, scaledHeight,
                0, 0, width, height
            );

            // Convert to base64
            const imageData = canvas.toDataURL('image/png', 0.9);
            
            return {
                imageData,
                dimensions: { width, height },
                originalDimensions: { 
                    width: scaledWidth, 
                    height: scaledHeight 
                }
            };

        } catch (error) {
            console.error('Failed to capture area:', error);
            throw new Error('Failed to capture screen area');
        }
    }

    /**
     * Capture full screen
     */
    async captureFullScreen() {
        if (!this.videoElement || !this.mediaStream) {
            throw new Error('Screen capture not initialized');
        }

        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = this.videoElement.videoWidth;
            canvas.height = this.videoElement.videoHeight;

            ctx.drawImage(this.videoElement, 0, 0);
            
            const imageData = canvas.toDataURL('image/png', 0.9);
            
            return {
                imageData,
                dimensions: { 
                    width: canvas.width, 
                    height: canvas.height 
                }
            };

        } catch (error) {
            console.error('Failed to capture full screen:', error);
            throw new Error('Failed to capture full screen');
        }
    }

    /**
     * Stop screen capture and cleanup
     */
    stopCapture() {
        if (this.mediaStream) {
            this.mediaStream.getTracks().forEach(track => track.stop());
            this.mediaStream = null;
        }

        if (this.videoElement) {
            this.videoElement.remove();
            this.videoElement = null;
        }
    }

    /**
     * Check if screen capture is supported
     */
    static isSupported() {
        return !!(navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia);
    }

    /**
     * Alternative capture method using html2canvas (fallback)
     */
    async captureElementArea(element, x, y, width, height) {
        try {
            // This would require html2canvas library
            // For now, we'll create a placeholder implementation
            
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = width;
            canvas.height = height;

            // Create a simple placeholder
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, width, height);
            
            // Add border
            ctx.strokeStyle = '#dee2e6';
            ctx.lineWidth = 2;
            ctx.strokeRect(1, 1, width - 2, height - 2);
            
            // Add text
            ctx.fillStyle = '#6c757d';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Screenshot Captured', width / 2, height / 2 - 10);
            ctx.fillText(`${width} × ${height}`, width / 2, height / 2 + 15);
            
            const imageData = canvas.toDataURL('image/png');
            
            return {
                imageData,
                dimensions: { width, height }
            };

        } catch (error) {
            console.error('Failed to capture element area:', error);
            throw new Error('Failed to capture element area');
        }
    }

    /**
     * Compress image data
     */
    compressImage(imageData, quality = 0.8) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = img.width;
                canvas.height = img.height;
                
                ctx.drawImage(img, 0, 0);
                
                const compressedData = canvas.toDataURL('image/jpeg', quality);
                resolve(compressedData);
            };
            img.src = imageData;
        });
    }

    /**
     * Resize image to specific dimensions
     */
    resizeImage(imageData, newWidth, newHeight) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                canvas.width = newWidth;
                canvas.height = newHeight;
                
                ctx.drawImage(img, 0, 0, newWidth, newHeight);
                
                const resizedData = canvas.toDataURL('image/png');
                resolve(resizedData);
            };
            img.src = imageData;
        });
    }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ScreenCaptureUtils;
} else {
    window.ScreenCaptureUtils = ScreenCaptureUtils;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 400px;
    min-height: 500px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

.container {
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 20px;
    color: white;
}

header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

header p {
    font-size: 14px;
    opacity: 0.9;
}

.capture-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.position-input {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 15px;
}

.position-input label {
    font-weight: 600;
    color: #555;
}

.position-input input {
    width: 60px;
    padding: 8px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
}

.position-input input:focus {
    outline: none;
    border-color: #667eea;
}

.capture-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.capture-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.capture-btn:active {
    transform: translateY(0);
}

.capture-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.status {
    margin-top: 10px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
    color: #666;
}

.status.capturing {
    background: #fff3cd;
    color: #856404;
}

.status.success {
    background: #d4edda;
    color: #155724;
}

.status.error {
    background: #f8d7da;
    color: #721c24;
}

.grid-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.grid-section h3 {
    margin-bottom: 15px;
    color: #333;
}

.grid-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.primary-btn, .secondary-btn {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.primary-btn {
    background: #28a745;
    color: white;
}

.primary-btn:hover {
    background: #218838;
}

.secondary-btn {
    background: #6c757d;
    color: white;
}

.secondary-btn:hover {
    background: #5a6268;
}

.grid-preview {
    min-height: 150px;
    border: 2px dashed #e1e5e9;
    border-radius: 8px;
    padding: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    align-items: flex-start;
    justify-content: flex-start;
}

.empty-grid {
    width: 100%;
    text-align: center;
    color: #999;
    font-style: italic;
    margin-top: 50px;
}

.grid-item {
    width: 60px;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-size: cover;
    background-position: center;
    position: relative;
    cursor: pointer;
}

.grid-item .position-label {
    position: absolute;
    top: -8px;
    left: -8px;
    background: #667eea;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 600;
}

.instructions {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    padding: 15px;
    font-size: 12px;
}

.instructions h4 {
    margin-bottom: 10px;
    color: #333;
}

.instructions ol {
    padding-left: 20px;
    line-height: 1.5;
}

.instructions li {
    margin-bottom: 5px;
    color: #555;
}

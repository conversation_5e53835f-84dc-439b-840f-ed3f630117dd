# Screenshot Array Capture Extension

A powerful Chrome extension that allows you to capture rectangular screenshots and arrange them in a customizable grid array.

## Features

- **Precise Rectangle Selection**: Click and drag to select any rectangular area on your screen
- **Grid-Based Organization**: Arrange screenshots in a grid using row and column coordinates (0,0 = top-left)
- **Visual Feedback**: See selection boundaries during capture (boundaries are not included in the final screenshot)
- **Frozen Screen Capture**: Screen freezes during selection for precise area selection
- **Combined Image Export**: Automatically combines all screenshots into a single image
- **Grid Preview**: Visual preview of your screenshot grid in the popup
- **Position Management**: Easy input of row/column positions with validation

## How to Use

1. **Install the Extension**:
   - Load the extension in Chrome Developer Mode
   - Add your logo images to the `icons/` folder (icon16.png, icon32.png, icon48.png, icon128.png)

2. **Capture Screenshots**:
   - Click the extension icon to open the popup
   - Enter the row and column position (0,0 = top-left corner)
   - Click "Start Capture" to freeze the screen
   - Click and drag to select the rectangular area
   - The screenshot will be automatically added to your grid

3. **Manage Your Grid**:
   - View all captured screenshots in the grid preview
   - Clear individual positions or the entire grid
   - Export the combined image when ready

4. **Export Final Image**:
   - Click "Export Combined Image" to download the final combined screenshot
   - All screenshots will be arranged according to their row/column positions

## Technical Details

### Files Structure
- `manifest.json` - Extension configuration
- `popup.html/css/js` - Main user interface
- `content.js/css` - Screen overlay and selection functionality
- `background.js` - Background service worker
- `capture.js` - Screen capture utilities
- `grid-manager.js` - Grid arrangement and image combination logic

### Permissions Required
- `activeTab` - Access to current tab for screenshot capture
- `desktopCapture` - Screen capture functionality
- `storage` - Store screenshot grid data
- `tabs` - Tab management for capture process

### Browser Compatibility
- Chrome 88+ (Manifest V3 support required)
- Edge 88+ (Chromium-based)

## Installation Instructions

1. Download or clone this extension
2. Add your logo images to the `icons/` folder:
   - `icon16.png` (16x16 pixels)
   - `icon32.png` (32x32 pixels)
   - `icon48.png` (48x48 pixels)
   - `icon128.png` (128x128 pixels)
3. Open Chrome and go to `chrome://extensions/`
4. Enable "Developer mode" in the top right
5. Click "Load unpacked" and select the extension folder
6. The extension icon will appear in your toolbar

## Usage Tips

- **Grid Coordinates**: Use (0,0) for top-left, (0,1) for top-right of first row, etc.
- **Overwriting**: You can overwrite existing positions by capturing to the same coordinates
- **Selection Quality**: Make precise selections - the boundary lines are not included in the screenshot
- **Export Format**: Final images are exported as PNG for best quality
- **Storage**: Screenshots are stored locally in your browser

## Troubleshooting

### Screen Capture Not Working
- Ensure you grant screen capture permissions when prompted
- Try refreshing the page and capturing again
- Check that you're using a supported browser (Chrome 88+)

### Selection Not Visible
- Make sure the overlay is properly loaded
- Try pressing ESC to cancel and start capture again
- Refresh the page if the overlay doesn't appear

### Export Issues
- Ensure you have at least one screenshot in the grid
- Check browser download permissions
- Try clearing the grid and recapturing if images appear corrupted

## Development

### Building from Source
1. Clone the repository
2. No build process required - this is a pure JavaScript extension
3. Load directly in Chrome Developer Mode

### Contributing
- Follow the existing code structure
- Test thoroughly with different screen sizes and browsers
- Ensure all features work with the latest Chrome version

## License

This extension is provided as-is for educational and personal use.

## Version History

- **v1.0.0** - Initial release with core functionality
  - Rectangle selection capture
  - Grid-based organization
  - Combined image export
  - Visual grid preview

---

**Created by**: Moond Sahab  
**Extension Type**: Chrome Extension (Manifest V3)  
**Last Updated**: June 2025

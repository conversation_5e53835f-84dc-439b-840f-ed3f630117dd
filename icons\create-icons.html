<!DOCTYPE html>
<html>
<head>
    <title>Create Extension Icons</title>
</head>
<body>
    <h2>Icon Generator for Screenshot Array Extension</h2>
    <p>This page will generate placeholder icons. You can replace these with your custom logo later.</p>
    
    <canvas id="icon16" width="16" height="16" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="icon32" width="32" height="32" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="icon48" width="48" height="48" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="icon128" width="128" height="128" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    
    <br><br>
    <button onclick="generateIcons()">Generate Icons</button>
    <button onclick="downloadIcons()">Download All Icons</button>
    
    <script>
        function generateIcons() {
            const sizes = [16, 32, 48, 128];
            
            sizes.forEach(size => {
                const canvas = document.getElementById(`icon${size}`);
                const ctx = canvas.getContext('2d');
                
                // Clear canvas
                ctx.clearRect(0, 0, size, size);
                
                // Background gradient
                const gradient = ctx.createLinearGradient(0, 0, size, size);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, size, size);
                
                // Draw camera/screenshot icon
                const centerX = size / 2;
                const centerY = size / 2;
                const iconSize = size * 0.6;
                
                // Camera body
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(centerX - iconSize/2, centerY - iconSize/3, iconSize, iconSize * 0.6);
                
                // Camera lens
                ctx.fillStyle = '#333333';
                ctx.beginPath();
                ctx.arc(centerX, centerY, iconSize * 0.2, 0, 2 * Math.PI);
                ctx.fill();
                
                // Grid lines
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = Math.max(1, size / 32);
                
                // Vertical lines
                for (let i = 1; i < 3; i++) {
                    const x = (size / 3) * i;
                    ctx.beginPath();
                    ctx.moveTo(x, size * 0.2);
                    ctx.lineTo(x, size * 0.8);
                    ctx.stroke();
                }
                
                // Horizontal lines
                for (let i = 1; i < 3; i++) {
                    const y = (size / 3) * i;
                    ctx.beginPath();
                    ctx.moveTo(size * 0.2, y);
                    ctx.lineTo(size * 0.8, y);
                    ctx.stroke();
                }
            });
        }
        
        function downloadIcons() {
            const sizes = [16, 32, 48, 128];
            
            sizes.forEach(size => {
                const canvas = document.getElementById(`icon${size}`);
                const link = document.createElement('a');
                link.download = `icon${size}.png`;
                link.href = canvas.toDataURL();
                link.click();
            });
        }
        
        // Generate icons on page load
        window.onload = generateIcons;
    </script>
</body>
</html>

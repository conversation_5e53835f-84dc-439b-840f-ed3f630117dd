// Content script for screenshot capture overlay

class ScreenshotCaptureOverlay {
    constructor() {
        this.isActive = false;
        this.isSelecting = false;
        this.startX = 0;
        this.startY = 0;
        this.currentX = 0;
        this.currentY = 0;
        this.overlay = null;
        this.selection = null;
        this.instructions = null;
        this.cancelButton = null;
        this.currentPosition = { row: 0, col: 0 };
        this.fullScreenData = null;

        this.setupMessageListener();

        // Bind methods to preserve 'this' context
        this.handleMouseDown = this.handleMouseDown.bind(this);
        this.handleMouseMove = this.handleMouseMove.bind(this);
        this.handleMouseUp = this.handleMouseUp.bind(this);
        this.handleKeyDown = this.handleKeyDown.bind(this);
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            switch (message.action) {
                case 'ping':
                    sendResponse({ success: true });
                    break;

                case 'showCaptureOverlay':
                    this.showOverlay(message.data);
                    sendResponse({ success: true });
                    break;

                case 'hideCaptureOverlay':
                    this.hideOverlay();
                    sendResponse({ success: true });
                    break;
            }
            return true;
        });
    }

    showOverlay(data) {
        if (this.isActive) {
            this.hideOverlay();
        }

        this.currentPosition = { row: data.row, col: data.col };
        this.fullScreenData = data.fullScreenData;
        this.isActive = true;

        // Prevent page scrolling and interaction
        document.body.style.overflow = 'hidden';
        document.body.style.userSelect = 'none';
        document.body.style.pointerEvents = 'none';

        this.createOverlayElements();
        this.attachEventListeners();

        console.log('Overlay activated for position:', this.currentPosition);
    }

    createOverlayElements() {
        // Main overlay - covers entire viewport
        this.overlay = document.createElement('div');
        this.overlay.className = 'screenshot-overlay';
        this.overlay.style.cssText = `
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 0.3) !important;
            z-index: 999999 !important;
            cursor: crosshair !important;
            user-select: none !important;
            pointer-events: auto !important;
        `;

        // Instructions
        this.instructions = document.createElement('div');
        this.instructions.className = 'screenshot-instructions';
        this.instructions.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            background: rgba(0, 0, 0, 0.8) !important;
            color: white !important;
            padding: 15px 25px !important;
            border-radius: 8px !important;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
            font-size: 14px !important;
            z-index: 1000000 !important;
            pointer-events: none !important;
        `;
        this.instructions.innerHTML = `
            <div style="font-weight: 600; margin-bottom: 5px;">Select Screenshot Area</div>
            <div style="font-size: 12px; opacity: 0.8;">Position: Row ${this.currentPosition.row}, Column ${this.currentPosition.col}</div>
            <div style="font-size: 12px; opacity: 0.8;">Click and drag to select area • ESC to cancel</div>
        `;

        // Cancel button
        this.cancelButton = document.createElement('button');
        this.cancelButton.className = 'screenshot-cancel';
        this.cancelButton.textContent = 'Cancel (ESC)';
        this.cancelButton.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            background: #dc3545 !important;
            color: white !important;
            border: none !important;
            padding: 10px 15px !important;
            border-radius: 6px !important;
            cursor: pointer !important;
            font-size: 14px !important;
            font-weight: 600 !important;
            z-index: 1000000 !important;
            pointer-events: auto !important;
        `;
        this.cancelButton.onclick = () => this.hideOverlay();

        // Selection rectangle
        this.selection = document.createElement('div');
        this.selection.className = 'screenshot-selection';
        this.selection.style.cssText = `
            position: fixed !important;
            border: 2px solid #007bff !important;
            background: rgba(0, 123, 255, 0.1) !important;
            pointer-events: none !important;
            z-index: 1000001 !important;
            display: none !important;
        `;

        // Add to page
        document.body.appendChild(this.overlay);
        document.body.appendChild(this.instructions);
        document.body.appendChild(this.cancelButton);
        document.body.appendChild(this.selection);

        console.log('Overlay elements created and added to page');
    }

    attachEventListeners() {
        // Mouse events on overlay
        this.overlay.addEventListener('mousedown', this.handleMouseDown, true);
        this.overlay.addEventListener('mousemove', this.handleMouseMove, true);
        this.overlay.addEventListener('mouseup', this.handleMouseUp, true);

        // Global events
        document.addEventListener('keydown', this.handleKeyDown, true);

        // Prevent context menu and other interactions
        this.overlay.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            e.stopPropagation();
        }, true);

        // Prevent any other mouse interactions
        this.overlay.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
        }, true);

        console.log('Event listeners attached');
    }

    handleMouseDown(e) {
        if (e.button !== 0) return; // Only left click

        console.log('Mouse down at:', e.clientX, e.clientY);

        this.isSelecting = true;
        this.startX = e.clientX;
        this.startY = e.clientY;
        this.currentX = e.clientX;
        this.currentY = e.clientY;

        this.selection.style.display = 'block';
        this.updateSelection();

        e.preventDefault();
        e.stopPropagation();
    }

    handleMouseMove(e) {
        if (!this.isSelecting) return;

        this.currentX = e.clientX;
        this.currentY = e.clientY;
        this.updateSelection();

        e.preventDefault();
        e.stopPropagation();
    }

    handleMouseUp(e) {
        if (!this.isSelecting || e.button !== 0) return;

        console.log('Mouse up, finishing selection');

        this.isSelecting = false;

        const rect = this.getSelectionRect();
        console.log('Selection rect:', rect);

        if (rect.width > 10 && rect.height > 10) {
            this.captureSelection(rect);
        } else {
            console.log('Selection too small, canceling');
            this.hideOverlay();
        }

        e.preventDefault();
        e.stopPropagation();
    }

    handleKeyDown(e) {
        if (e.key === 'Escape') {
            this.hideOverlay();
        }
    }

    updateSelection() {
        const rect = this.getSelectionRect();

        this.selection.style.left = rect.left + 'px';
        this.selection.style.top = rect.top + 'px';
        this.selection.style.width = rect.width + 'px';
        this.selection.style.height = rect.height + 'px';
    }

    getSelectionRect() {
        const left = Math.min(this.startX, this.currentX);
        const top = Math.min(this.startY, this.currentY);
        const width = Math.abs(this.currentX - this.startX);
        const height = Math.abs(this.currentY - this.startY);

        return { left, top, width, height };
    }

    async captureSelection(rect) {
        try {
            console.log('Starting capture for rect:', rect);

            // Create canvas for the selected area
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            canvas.width = rect.width;
            canvas.height = rect.height;

            if (this.fullScreenData) {
                // Use the full screen data we captured earlier
                const img = new Image();
                img.onload = () => {
                    // Calculate scale factors
                    const scaleX = img.width / window.innerWidth;
                    const scaleY = img.height / window.innerHeight;

                    // Calculate source coordinates
                    const sourceX = rect.left * scaleX;
                    const sourceY = rect.top * scaleY;
                    const sourceWidth = rect.width * scaleX;
                    const sourceHeight = rect.height * scaleY;

                    // Draw the selected area
                    ctx.drawImage(
                        img,
                        sourceX, sourceY, sourceWidth, sourceHeight,
                        0, 0, rect.width, rect.height
                    );

                    // Convert to base64
                    const imageData = canvas.toDataURL('image/png');

                    console.log('Capture successful, sending to background');

                    // Send to background script
                    chrome.runtime.sendMessage({
                        action: 'captureComplete',
                        data: {
                            row: this.currentPosition.row,
                            col: this.currentPosition.col,
                            imageData: imageData,
                            dimensions: {
                                width: rect.width,
                                height: rect.height
                            }
                        }
                    });

                    this.hideOverlay();
                };
                img.src = this.fullScreenData;
            } else {
                throw new Error('No screen data available');
            }

        } catch (error) {
            console.error('Capture failed:', error);
            this.hideOverlay();
        }
    }



    hideOverlay() {
        if (!this.isActive) return;

        console.log('Hiding overlay');

        this.isActive = false;
        this.isSelecting = false;

        // Remove event listeners first
        document.removeEventListener('keydown', this.handleKeyDown, true);

        // Remove elements
        if (this.overlay) {
            this.overlay.remove();
            this.overlay = null;
        }
        if (this.instructions) {
            this.instructions.remove();
            this.instructions = null;
        }
        if (this.cancelButton) {
            this.cancelButton.remove();
            this.cancelButton = null;
        }
        if (this.selection) {
            this.selection.remove();
            this.selection = null;
        }

        // Restore page functionality
        document.body.style.overflow = '';
        document.body.style.userSelect = '';
        document.body.style.pointerEvents = '';

        // Clear screen data
        this.fullScreenData = null;

        console.log('Overlay hidden and page restored');
    }
}

// Initialize the overlay handler
const screenshotOverlay = new ScreenshotCaptureOverlay();

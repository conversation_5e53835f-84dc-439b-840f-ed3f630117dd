// Content script for screenshot capture overlay

class ScreenshotCaptureOverlay {
    constructor() {
        this.isActive = false;
        this.isSelecting = false;
        this.startX = 0;
        this.startY = 0;
        this.currentX = 0;
        this.currentY = 0;
        this.overlay = null;
        this.selection = null;
        this.instructions = null;
        this.cancelButton = null;
        this.currentPosition = { row: 0, col: 0 };

        this.setupMessageListener();
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            switch (message.action) {
                case 'ping':
                    sendResponse({ success: true });
                    break;

                case 'showCaptureOverlay':
                    this.showOverlay(message.data);
                    sendResponse({ success: true });
                    break;

                case 'hideCaptureOverlay':
                    this.hideOverlay();
                    sendResponse({ success: true });
                    break;
            }
            return true;
        });
    }

    showOverlay(data) {
        if (this.isActive) {
            this.hideOverlay();
        }

        this.currentPosition = { row: data.row, col: data.col };
        this.isActive = true;

        // Prevent page scrolling
        document.body.classList.add('screenshot-capture-active');

        this.createOverlayElements();
        this.attachEventListeners();
    }

    createOverlayElements() {
        // Main overlay
        this.overlay = document.createElement('div');
        this.overlay.className = 'screenshot-overlay';

        // Instructions
        this.instructions = document.createElement('div');
        this.instructions.className = 'screenshot-instructions';
        this.instructions.innerHTML = `
            <div class="title">Select Screenshot Area</div>
            <div class="subtitle">Position: Row ${this.currentPosition.row}, Column ${this.currentPosition.col}</div>
            <div class="subtitle">Click and drag to select area • ESC to cancel</div>
        `;

        // Cancel button
        this.cancelButton = document.createElement('button');
        this.cancelButton.className = 'screenshot-cancel';
        this.cancelButton.textContent = 'Cancel';
        this.cancelButton.onclick = () => this.hideOverlay();

        // Selection rectangle
        this.selection = document.createElement('div');
        this.selection.className = 'screenshot-selection';
        this.selection.style.display = 'none';

        // Add to page
        document.body.appendChild(this.overlay);
        document.body.appendChild(this.instructions);
        document.body.appendChild(this.cancelButton);
        document.body.appendChild(this.selection);
    }

    attachEventListeners() {
        this.overlay.addEventListener('mousedown', this.handleMouseDown.bind(this));
        this.overlay.addEventListener('mousemove', this.handleMouseMove.bind(this));
        this.overlay.addEventListener('mouseup', this.handleMouseUp.bind(this));

        document.addEventListener('keydown', this.handleKeyDown.bind(this));

        // Prevent context menu
        this.overlay.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    handleMouseDown(e) {
        if (e.button !== 0) return; // Only left click

        this.isSelecting = true;
        this.startX = e.clientX;
        this.startY = e.clientY;
        this.currentX = e.clientX;
        this.currentY = e.clientY;

        this.selection.style.display = 'block';
        this.updateSelection();

        e.preventDefault();
    }

    handleMouseMove(e) {
        if (!this.isSelecting) return;

        this.currentX = e.clientX;
        this.currentY = e.clientY;
        this.updateSelection();

        e.preventDefault();
    }

    handleMouseUp(e) {
        if (!this.isSelecting || e.button !== 0) return;

        this.isSelecting = false;

        const rect = this.getSelectionRect();
        if (rect.width > 10 && rect.height > 10) {
            this.captureSelection(rect);
        } else {
            this.hideOverlay();
        }

        e.preventDefault();
    }

    handleKeyDown(e) {
        if (e.key === 'Escape') {
            this.hideOverlay();
        }
    }

    updateSelection() {
        const rect = this.getSelectionRect();

        this.selection.style.left = rect.left + 'px';
        this.selection.style.top = rect.top + 'px';
        this.selection.style.width = rect.width + 'px';
        this.selection.style.height = rect.height + 'px';
    }

    getSelectionRect() {
        const left = Math.min(this.startX, this.currentX);
        const top = Math.min(this.startY, this.currentY);
        const width = Math.abs(this.currentX - this.startX);
        const height = Math.abs(this.currentY - this.startY);

        return { left, top, width, height };
    }

    async captureSelection(rect) {
        try {
            // Hide overlay temporarily for clean capture
            this.overlay.style.display = 'none';
            this.instructions.style.display = 'none';
            this.cancelButton.style.display = 'none';
            this.selection.style.display = 'none';

            // Wait a moment for UI to hide
            await new Promise(resolve => setTimeout(resolve, 100));

            // Capture the screen
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Set canvas size to selection
            canvas.width = rect.width;
            canvas.height = rect.height;

            // Use html2canvas or similar approach for capturing
            await this.captureScreenArea(canvas, ctx, rect);

            // Convert to base64
            const imageData = canvas.toDataURL('image/png');

            // Send to background script
            chrome.runtime.sendMessage({
                action: 'captureComplete',
                data: {
                    row: this.currentPosition.row,
                    col: this.currentPosition.col,
                    imageData: imageData,
                    dimensions: {
                        width: rect.width,
                        height: rect.height
                    }
                }
            });

            this.hideOverlay();

        } catch (error) {
            console.error('Capture failed:', error);
            this.hideOverlay();
        }
    }

    async captureScreenArea(canvas, ctx, rect) {
        try {
            // Use Chrome's tab capture API through background script
            const response = await chrome.runtime.sendMessage({
                action: 'captureTab',
                data: { rect }
            });

            if (response.success && response.imageData) {
                // Load the captured image
                const img = new Image();
                img.onload = () => {
                    // Calculate the area to extract
                    const scaleX = img.width / window.innerWidth;
                    const scaleY = img.height / window.innerHeight;

                    const sourceX = rect.left * scaleX;
                    const sourceY = rect.top * scaleY;
                    const sourceWidth = rect.width * scaleX;
                    const sourceHeight = rect.height * scaleY;

                    // Draw the selected area
                    ctx.drawImage(
                        img,
                        sourceX, sourceY, sourceWidth, sourceHeight,
                        0, 0, rect.width, rect.height
                    );
                };
                img.src = response.imageData;
            } else {
                throw new Error('Tab capture failed');
            }

        } catch (error) {
            console.warn('Screen capture failed, using fallback method');

            // Create a placeholder with selection info
            ctx.fillStyle = '#f8f9fa';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add border
            ctx.strokeStyle = '#007bff';
            ctx.lineWidth = 2;
            ctx.strokeRect(1, 1, canvas.width - 2, canvas.height - 2);

            // Add text
            ctx.fillStyle = '#333';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Screenshot Captured', canvas.width / 2, canvas.height / 2 - 20);

            ctx.font = '14px Arial';
            ctx.fillStyle = '#666';
            ctx.fillText(`Position: ${this.currentPosition.row}, ${this.currentPosition.col}`, canvas.width / 2, canvas.height / 2);
            ctx.fillText(`Size: ${rect.width} × ${rect.height}`, canvas.width / 2, canvas.height / 2 + 20);

            // Add timestamp
            const now = new Date();
            ctx.font = '12px Arial';
            ctx.fillText(now.toLocaleTimeString(), canvas.width / 2, canvas.height / 2 + 40);
        }
    }

    hideOverlay() {
        if (!this.isActive) return;

        this.isActive = false;
        this.isSelecting = false;

        // Remove elements
        if (this.overlay) {
            this.overlay.remove();
            this.overlay = null;
        }
        if (this.instructions) {
            this.instructions.remove();
            this.instructions = null;
        }
        if (this.cancelButton) {
            this.cancelButton.remove();
            this.cancelButton = null;
        }
        if (this.selection) {
            this.selection.remove();
            this.selection = null;
        }

        // Restore page scrolling
        document.body.classList.remove('screenshot-capture-active');

        // Remove event listeners
        document.removeEventListener('keydown', this.handleKeyDown.bind(this));
    }
}

// Initialize the overlay handler
const screenshotOverlay = new ScreenshotCaptureOverlay();

/* Screenshot capture overlay styles */
.screenshot-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    z-index: 999999;
    cursor: crosshair;
    user-select: none;
    pointer-events: auto;
}

.screenshot-selection {
    position: absolute;
    border: 2px solid #007bff;
    background: rgba(0, 123, 255, 0.1);
    pointer-events: none;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);
}

.screenshot-selection::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px dashed #ffffff;
    animation: dash 1s linear infinite;
}

@keyframes dash {
    to {
        stroke-dashoffset: -20;
    }
}

.screenshot-instructions {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    z-index: 1000000;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.screenshot-instructions .title {
    font-weight: 600;
    margin-bottom: 5px;
}

.screenshot-instructions .subtitle {
    font-size: 12px;
    opacity: 0.8;
}

.screenshot-cancel {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #dc3545;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    z-index: 1000000;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    transition: background 0.2s;
}

.screenshot-cancel:hover {
    background: #c82333;
}

/* Hide scrollbars during capture */
.screenshot-capture-active {
    overflow: hidden !important;
}

.screenshot-capture-active * {
    overflow: hidden !important;
}

/* Crosshair cursor enhancement */
.screenshot-overlay::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 1px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.5);
    pointer-events: none;
    z-index: 1000001;
    transition: left 0.1s ease;
}

.screenshot-overlay::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 1px;
    background: rgba(255, 255, 255, 0.5);
    pointer-events: none;
    z-index: 1000001;
    transition: top 0.1s ease;
}

// Popup script for Screenshot Array Extension

class ScreenshotArrayPopup {
    constructor() {
        this.currentGrid = {};
        this.isCapturing = false;

        this.initializeElements();
        this.attachEventListeners();
        this.loadStoredGrid();
    }

    initializeElements() {
        this.rowInput = document.getElementById('row');
        this.colInput = document.getElementById('col');
        this.captureBtn = document.getElementById('captureBtn');
        this.status = document.getElementById('status');
        this.gridPreview = document.getElementById('gridPreview');
        this.clearGridBtn = document.getElementById('clearGrid');
        this.exportGridBtn = document.getElementById('exportGrid');
    }

    attachEventListeners() {
        this.captureBtn.addEventListener('click', () => this.startCapture());
        this.clearGridBtn.addEventListener('click', () => this.clearGrid());
        this.exportGridBtn.addEventListener('click', () => this.exportGrid());

        // Input validation
        this.rowInput.addEventListener('input', () => this.validateInputs());
        this.colInput.addEventListener('input', () => this.validateInputs());

        // Listen for storage changes
        chrome.storage.onChanged.addListener((changes) => {
            if (changes.screenshotGrid) {
                this.loadStoredGrid();
            }
        });
    }

    validateInputs() {
        const row = parseInt(this.rowInput.value);
        const col = parseInt(this.colInput.value);

        const isValid = !isNaN(row) && !isNaN(col) && row >= 0 && col >= 0 && row <= 10 && col <= 10;

        this.captureBtn.disabled = !isValid || this.isCapturing;

        if (!isValid && (this.rowInput.value || this.colInput.value)) {
            this.updateStatus('Please enter valid row and column numbers (0-10)', 'error');
        } else if (isValid) {
            const key = `${row}-${col}`;
            if (this.currentGrid[key]) {
                this.updateStatus(`Position ${row},${col} already has a screenshot`, 'error');
            } else {
                this.updateStatus('Ready to capture', '');
            }
        }
    }

    async startCapture() {
        const row = parseInt(this.rowInput.value);
        const col = parseInt(this.colInput.value);

        if (isNaN(row) || isNaN(col)) {
            this.updateStatus('Please enter valid row and column numbers', 'error');
            return;
        }

        const key = `${row}-${col}`;
        if (this.currentGrid[key]) {
            const overwrite = confirm(`Position ${row},${col} already has a screenshot. Overwrite?`);
            if (!overwrite) return;
        }

        try {
            this.isCapturing = true;
            this.captureBtn.disabled = true;
            this.updateStatus('Preparing capture...', 'capturing');

            // Send message to background script first
            const response = await this.sendMessage({
                action: 'startCapture',
                data: { row, col }
            });

            if (response.success) {
                this.updateStatus('Screen frozen! Click and drag to select area', 'capturing');
                // Close popup after a short delay to allow user to see the message
                setTimeout(() => {
                    window.close();
                }, 1000);
            } else {
                throw new Error(response.error);
            }

        } catch (error) {
            console.error('Capture failed:', error);
            this.updateStatus(`Error: ${error.message}`, 'error');
            this.isCapturing = false;
            this.captureBtn.disabled = false;
        }
    }

    async loadStoredGrid() {
        try {
            const response = await this.sendMessage({ action: 'getGrid' });

            if (response.success) {
                this.currentGrid = response.grid;
                this.updateGridPreview();
                this.updateStatus(`${Object.keys(this.currentGrid).length} screenshots in grid`, 'success');
            }
        } catch (error) {
            console.error('Failed to load grid:', error);
            this.updateStatus('Failed to load stored screenshots', 'error');
        }
    }

    updateGridPreview() {
        if (Object.keys(this.currentGrid).length === 0) {
            this.gridPreview.innerHTML = '<div class="empty-grid">No screenshots captured yet</div>';
            this.exportGridBtn.disabled = true;
            return;
        }

        this.exportGridBtn.disabled = false;

        // Calculate grid dimensions
        let maxRow = 0, maxCol = 0;
        Object.values(this.currentGrid).forEach(item => {
            maxRow = Math.max(maxRow, item.row);
            maxCol = Math.max(maxCol, item.col);
        });

        // Create grid visualization
        let gridHTML = '<div class="grid-container" style="display: grid; gap: 2px;">';
        gridHTML += `grid-template-columns: repeat(${maxCol + 1}, 1fr); `;
        gridHTML += `grid-template-rows: repeat(${maxRow + 1}, 1fr);">`;

        for (let row = 0; row <= maxRow; row++) {
            for (let col = 0; col <= maxCol; col++) {
                const key = `${row}-${col}`;
                const item = this.currentGrid[key];

                if (item) {
                    gridHTML += `
                        <div class="grid-item" 
                             style="background-image: url(${item.imageData}); grid-row: ${row + 1}; grid-column: ${col + 1};"
                             title="Screenshot at ${row},${col}">
                            <div class="position-label">${row},${col}</div>
                        </div>
                    `;
                } else {
                    gridHTML += `
                        <div class="grid-item empty" 
                             style="background: #f8f9fa; border: 1px dashed #ccc; grid-row: ${row + 1}; grid-column: ${col + 1};"
                             title="Empty position ${row},${col}">
                            <div class="position-label">${row},${col}</div>
                        </div>
                    `;
                }
            }
        }

        gridHTML += '</div>';
        this.gridPreview.innerHTML = gridHTML;
    }

    async clearGrid() {
        if (Object.keys(this.currentGrid).length === 0) return;

        const confirm = window.confirm('Are you sure you want to clear all screenshots?');
        if (!confirm) return;

        try {
            const response = await this.sendMessage({ action: 'clearGrid' });

            if (response.success) {
                this.currentGrid = {};
                this.updateGridPreview();
                this.updateStatus('Grid cleared successfully', 'success');
            } else {
                throw new Error(response.error);
            }
        } catch (error) {
            console.error('Failed to clear grid:', error);
            this.updateStatus('Failed to clear grid', 'error');
        }
    }

    async exportGrid() {
        if (Object.keys(this.currentGrid).length === 0) {
            this.updateStatus('No screenshots to export', 'error');
            return;
        }

        try {
            this.updateStatus('Generating combined image...', 'capturing');

            // Create combined image
            const combinedImage = await this.createCombinedImage();

            // Download the image
            this.downloadImage(combinedImage, 'screenshot-array.png');

            this.updateStatus('Image exported successfully!', 'success');

        } catch (error) {
            console.error('Export failed:', error);
            this.updateStatus('Failed to export image', 'error');
        }
    }

    async createCombinedImage() {
        // Calculate grid dimensions and find max image sizes
        let maxRow = 0, maxCol = 0;
        let maxWidth = 0, maxHeight = 0;

        Object.values(this.currentGrid).forEach(item => {
            maxRow = Math.max(maxRow, item.row);
            maxCol = Math.max(maxCol, item.col);
            if (item.dimensions) {
                maxWidth = Math.max(maxWidth, item.dimensions.width);
                maxHeight = Math.max(maxHeight, item.dimensions.height);
            }
        });

        // Create canvas for combined image
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size
        canvas.width = (maxCol + 1) * maxWidth;
        canvas.height = (maxRow + 1) * maxHeight;

        // Fill with white background
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Load and draw each image
        const imagePromises = Object.values(this.currentGrid).map(item => {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => {
                    const x = item.col * maxWidth;
                    const y = item.row * maxHeight;
                    ctx.drawImage(img, x, y, maxWidth, maxHeight);
                    resolve();
                };
                img.src = item.imageData;
            });
        });

        await Promise.all(imagePromises);

        return canvas.toDataURL('image/png');
    }

    downloadImage(dataUrl, filename) {
        const link = document.createElement('a');
        link.download = filename;
        link.href = dataUrl;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    updateStatus(message, type = '') {
        this.status.textContent = message;
        this.status.className = `status ${type}`;
    }

    sendMessage(message) {
        return new Promise((resolve) => {
            chrome.runtime.sendMessage(message, resolve);
        });
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ScreenshotArrayPopup();
});

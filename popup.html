<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Screenshot Array Capture</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Screenshot Array</h1>
            <p>Capture and arrange screenshots in a grid</p>
        </header>

        <div class="capture-section">
            <div class="position-input">
                <label for="row">Row:</label>
                <input type="number" id="row" min="0" max="10" value="0" placeholder="0">
                
                <label for="col">Column:</label>
                <input type="number" id="col" min="0" max="10" value="0" placeholder="0">
            </div>
            
            <button id="captureBtn" class="capture-btn">
                📸 Start Capture
            </button>
            
            <div class="status" id="status">
                Ready to capture
            </div>
        </div>

        <div class="grid-section">
            <h3>Current Grid</h3>
            <div class="grid-controls">
                <button id="clearGrid" class="secondary-btn">Clear All</button>
                <button id="exportGrid" class="primary-btn">Export Combined Image</button>
            </div>
            
            <div class="grid-preview" id="gridPreview">
                <div class="empty-grid">No screenshots captured yet</div>
            </div>
        </div>

        <div class="instructions">
            <h4>How to use:</h4>
            <ol>
                <li>Enter row and column position (0,0 = top-left)</li>
                <li>Click "Start Capture" to freeze the screen</li>
                <li>Draw a rectangle to select the area</li>
                <li>Screenshot will be added to the grid</li>
                <li>Repeat for other positions</li>
                <li>Export the final combined image</li>
            </ol>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>

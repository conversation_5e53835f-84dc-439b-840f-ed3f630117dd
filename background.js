// Background service worker for Screenshot Array Extension

class ScreenshotArrayBackground {
    constructor() {
        this.setupEventListeners();
        this.captureInProgress = false;
    }

    setupEventListeners() {
        // Handle extension installation
        chrome.runtime.onInstalled.addListener(() => {
            console.log('Screenshot Array Extension installed');
            this.initializeStorage();
        });

        // Handle messages from popup and content scripts
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Handle tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && this.captureInProgress) {
                this.captureInProgress = false;
            }
        });
    }

    async initializeStorage() {
        try {
            const result = await chrome.storage.local.get(['screenshotGrid']);
            if (!result.screenshotGrid) {
                await chrome.storage.local.set({
                    screenshotGrid: {},
                    gridSettings: {
                        maxRows: 10,
                        maxCols: 10
                    }
                });
            }
        } catch (error) {
            console.error('Failed to initialize storage:', error);
        }
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'startCapture':
                    await this.startScreenCapture(message.data, sendResponse);
                    break;

                case 'captureComplete':
                    await this.handleCaptureComplete(message.data, sendResponse);
                    break;

                case 'getGrid':
                    await this.getStoredGrid(sendResponse);
                    break;

                case 'clearGrid':
                    await this.clearGrid(sendResponse);
                    break;

                case 'exportGrid':
                    await this.exportCombinedImage(sendResponse);
                    break;

                case 'captureTab':
                    await this.captureCurrentTab(message.data, sendResponse);
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async startScreenCapture(data, sendResponse) {
        try {
            this.captureInProgress = true;

            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab) {
                throw new Error('No active tab found');
            }

            // Check if tab URL is restricted
            if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://') ||
                tab.url.startsWith('edge://') || tab.url.startsWith('about:')) {
                throw new Error('Cannot capture browser internal pages. Please open a regular website first.');
            }

            // First capture the full screen
            const fullScreenData = await chrome.tabs.captureVisibleTab(tab.windowId, {
                format: 'png',
                quality: 100
            });

            // Inject content script if not already injected
            await this.ensureContentScriptInjected(tab.id);

            // Send message to content script to start overlay with full screen data
            await chrome.tabs.sendMessage(tab.id, {
                action: 'showCaptureOverlay',
                data: {
                    row: data.row,
                    col: data.col,
                    fullScreenData: fullScreenData
                }
            });

            sendResponse({ success: true });
        } catch (error) {
            this.captureInProgress = false;
            console.error('Failed to start capture:', error);

            let errorMessage = error.message;
            if (error.message.includes('chrome://')) {
                errorMessage = 'Cannot capture browser pages. Please open a website like google.com and try again.';
            }

            sendResponse({ success: false, error: errorMessage });
        }
    }

    async ensureContentScriptInjected(tabId) {
        try {
            // Try to ping the content script
            await chrome.tabs.sendMessage(tabId, { action: 'ping' });
        } catch (error) {
            // Content script not injected, inject it
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content.js']
            });
        }
    }

    async handleCaptureComplete(data, sendResponse) {
        try {
            // Store the screenshot in the grid
            const result = await chrome.storage.local.get(['screenshotGrid']);
            const grid = result.screenshotGrid || {};

            const key = `${data.row}-${data.col}`;
            grid[key] = {
                row: data.row,
                col: data.col,
                imageData: data.imageData,
                timestamp: Date.now(),
                dimensions: data.dimensions
            };

            await chrome.storage.local.set({ screenshotGrid: grid });

            this.captureInProgress = false;
            sendResponse({ success: true });
        } catch (error) {
            console.error('Failed to store screenshot:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async getStoredGrid(sendResponse) {
        try {
            const result = await chrome.storage.local.get(['screenshotGrid']);
            sendResponse({ success: true, grid: result.screenshotGrid || {} });
        } catch (error) {
            console.error('Failed to get grid:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async clearGrid(sendResponse) {
        try {
            await chrome.storage.local.set({ screenshotGrid: {} });
            sendResponse({ success: true });
        } catch (error) {
            console.error('Failed to clear grid:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async exportCombinedImage(sendResponse) {
        try {
            const result = await chrome.storage.local.get(['screenshotGrid']);
            const grid = result.screenshotGrid || {};

            if (Object.keys(grid).length === 0) {
                throw new Error('No screenshots to export');
            }

            // Calculate grid dimensions
            let maxRow = 0, maxCol = 0;
            Object.values(grid).forEach(item => {
                maxRow = Math.max(maxRow, item.row);
                maxCol = Math.max(maxCol, item.col);
            });

            // Create combined image data
            const combinedImageData = await this.createCombinedImage(grid, maxRow + 1, maxCol + 1);

            sendResponse({
                success: true,
                imageData: combinedImageData,
                dimensions: { rows: maxRow + 1, cols: maxCol + 1 }
            });
        } catch (error) {
            console.error('Failed to export grid:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async createCombinedImage(grid, rows, cols) {
        // This will be implemented in the popup script since we need canvas access
        // Return the grid data for processing in the popup
        return { grid, rows, cols };
    }

    async captureCurrentTab(data, sendResponse) {
        try {
            // Get the active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab) {
                throw new Error('No active tab found');
            }

            // Check if the tab URL is a restricted chrome:// URL
            if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://') ||
                tab.url.startsWith('edge://') || tab.url.startsWith('about:')) {
                throw new Error('Cannot capture restricted browser pages. Please navigate to a regular website first.');
            }

            // Capture the visible tab
            const imageData = await chrome.tabs.captureVisibleTab(tab.windowId, {
                format: 'png',
                quality: 100
            });

            sendResponse({
                success: true,
                imageData: imageData
            });

        } catch (error) {
            console.error('Failed to capture tab:', error);

            // Provide user-friendly error messages
            let errorMessage = error.message;
            if (error.message.includes('chrome://') || error.message.includes('Cannot capture')) {
                errorMessage = 'Cannot capture browser internal pages. Please open a regular website (like google.com) and try again.';
            } else if (error.message.includes('permission')) {
                errorMessage = 'Permission denied. Please ensure the extension has access to capture screenshots.';
            }

            sendResponse({ success: false, error: errorMessage });
        }
    }
}

// Initialize the background service
new ScreenshotArrayBackground();
